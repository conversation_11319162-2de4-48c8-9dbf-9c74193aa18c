import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from typing import List, Dict, Optional

# --- Настройки визуализации ---
def set_plot_style():
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.rcParams.update({
        'figure.figsize': (14, 7),
        'axes.titlesize': 16,
        'axes.titleweight': 'bold',
        'axes.titlepad': 25,
        'axes.labelsize': 13,
        'xtick.labelsize': 11,
        'ytick.labelsize': 11,
        'legend.fontsize': 10,
        'grid.alpha': 0.4,
        'lines.markersize': 8,
        'lines.linewidth': 2.8,
        'font.family': 'DejaVu Sans'
    })

# --- Цветовые схемы ---
def get_color_schemes():
    # Контрастные цвета для городов
    city_colors = [
        '#1f77b4',  # синий
        '#ff7f0e',  # оранжевый
        '#2ca02c',  # зеленый
        '#d62728',  # красный
        '#9467bd',  # фиолетовый
        '#8c564b',  # коричневый
        '#e377c2',  # розовый
        '#7f7f7f',  # серый
        '#bcbd22',  # оливковый
        '#17becf'   # голубой
    ]

    # Стили для срочных/отложенных заказов
    order_styles = {
        'urgent': {'alpha': 0.9, 'linestyle': '-', 'linewidth': 3.0, 'marker': 'o', 'markersize': 9},
        'delayed': {'alpha': 0.7, 'linestyle': '--', 'linewidth': 2.5, 'marker': 's', 'markersize': 8}
    }

    return city_colors, order_styles

# --- Загрузка и подготовка данных ---
def load_and_prepare_data(filepath: str) -> pd.DataFrame:
    try:
        df = pd.read_excel(filepath)
        for col in ['trip_time', 'order_time', 'offer_time', 'assign_time', 'arrive_time']:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce', dayfirst=True)
        df['day_order'] = df['order_time'].dt.day
        return df
    except Exception as e:
        print(f"Ошибка при загрузке данных: {e}")
        return pd.DataFrame()

# --- Алгоритм выделения отложенных заказов ---
def mark_delayed_orders(df: pd.DataFrame, threshold_min: int = 60) -> pd.DataFrame:
    # threshold_min — минимальное количество минут между заказом и подачей, чтобы считать заказ отложенным
    df = df.copy()
    if 'order_time' in df.columns and 'assign_time' in df.columns:
        df['delay_min'] = (df['assign_time'] - df['order_time']).dt.total_seconds() / 60
        df['is_delayed'] = df['delay_min'] > threshold_min
    else:
        df['is_delayed'] = False
    return df

# --- Агрегация и расчет конверсий ---
def aggregate_metrics(df: pd.DataFrame, segment_col: Optional[str] = None) -> pd.DataFrame:
    group_cols = ['day_order', 'city']
    if segment_col:
        group_cols.append(segment_col)
    agg = df.groupby(group_cols, as_index=False).agg(
        cnt_order=('id_order', 'count'),
        cnt_offer=('offer_time', 'count'),
        cnt_assign=('assign_time', 'count'),
        cnt_arrive=('arrive_time', 'count'),
        cnt_trip=('trip_time', 'count')
    )
    for a, b, name in [
        ('cnt_trip', 'cnt_order', 'order2trip'),
        ('cnt_offer', 'cnt_order', 'order2offer'),
        ('cnt_assign', 'cnt_offer', 'offer2assign'),
        ('cnt_arrive', 'cnt_assign', 'assign2arrive'),
        ('cnt_trip', 'cnt_arrive', 'arrive2trip')
    ]:
        agg[name] = np.where(agg[b] > 0, agg[a] / agg[b], np.nan)
    return agg

# --- Алерт по выбросам ---
def alert_outliers(df: pd.DataFrame, metric_cols: List[str], threshold: float = 2.5):
    alerts = []
    for col in metric_cols:
        if col in df.columns:
            med = df[col].median()
            mad = np.median(np.abs(df[col] - med))
            if mad == 0:
                continue
            outliers = df[np.abs(df[col] - med) > threshold * mad]
            for _, row in outliers.iterrows():
                alerts.append(f"Выброс по {col}: {row.to_dict()}")
    if alerts:
        print("\nАЛЕРТЫ ПО ВЫБРОСАМ:")
        for alert in alerts:
            print(alert)

# --- Визуализация ---
def plot_metric(df: pd.DataFrame, metric_col: str, title: str, ylim: tuple = (0, 100), cities: List[str] = None, segment_col: Optional[str] = None):
    set_plot_style()
    city_colors, order_styles = get_color_schemes()

    if cities is None:
        cities = df['city'].unique()

    fig, ax = plt.subplots(figsize=(15, 8))

    if segment_col and segment_col in df.columns:
        segments = df[segment_col].unique()
        for i, city in enumerate(cities):
            city_color = city_colors[i % len(city_colors)]
            for seg in segments:
                data = df[(df['city'] == city) & (df[segment_col] == seg)].sort_values('day_order')
                if data.empty:
                    continue

                label = f"{city} - {'Отложенный' if seg else 'Срочный'}"
                style_key = 'delayed' if seg else 'urgent'
                style = order_styles[style_key].copy()
                style['color'] = city_color

                line = ax.plot(data['day_order'], data[metric_col]*100, label=label, **style)[0]

                # Улучшенное размещение подписей с проверкой перекрытий
                for j, (x, y) in enumerate(zip(data['day_order'], data[metric_col]*100)):
                    # Смещение для избежания перекрытий
                    offset_y = 8 if seg else -15
                    offset_x = (-1)**j * 3  # Чередующееся горизонтальное смещение

                    ax.annotate(f"{y:.1f}%", (x, y),
                              textcoords="offset points",
                              xytext=(offset_x, offset_y),
                              ha='center', va='bottom' if seg else 'top',
                              fontsize=9, fontweight='bold',
                              color=city_color,
                              bbox=dict(boxstyle='round,pad=0.2',
                                       facecolor='white',
                                       alpha=0.8,
                                       edgecolor=city_color,
                                       linewidth=0.5))
    else:
        for i, city in enumerate(cities):
            city_color = city_colors[i % len(city_colors)]
            data = df[df['city'] == city].sort_values('day_order')
            if data.empty:
                continue

            ax.plot(data['day_order'], data[metric_col]*100,
                   label=city, marker='o', color=city_color,
                   linewidth=3, markersize=8, alpha=0.9)

            for x, y in zip(data['day_order'], data[metric_col]*100):
                ax.annotate(f"{y:.1f}%", (x, y),
                          textcoords="offset points",
                          xytext=(0, 10),
                          ha='center', va='bottom',
                          fontsize=9, fontweight='bold',
                          color=city_color,
                          bbox=dict(boxstyle='round,pad=0.2',
                                   facecolor='white',
                                   alpha=0.8,
                                   edgecolor=city_color,
                                   linewidth=0.5))

    # Улучшенное оформление графика
    ax.set_title(title, fontsize=18, fontweight='bold', pad=25)
    ax.set_xlabel("День месяца", fontsize=14, fontweight='bold')
    ax.set_ylabel("Конверсия, %", fontsize=14, fontweight='bold')
    ax.set_ylim(ylim)
    ax.set_xticks(sorted(df['day_order'].unique()))

    # Улучшенная сетка
    ax.grid(True, linestyle='-', alpha=0.3, linewidth=0.8, which='major')
    ax.grid(True, linestyle=':', alpha=0.2, linewidth=0.5, which='minor')
    ax.set_axisbelow(True)

    # Улучшенная легенда
    legend = ax.legend(title='Город/Тип заказа',
                      bbox_to_anchor=(1.02, 1),
                      loc='upper left',
                      borderaxespad=0.,
                      frameon=True,
                      fancybox=True,
                      shadow=True,
                      title_fontsize=12,
                      fontsize=10)
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.95)

    # Вспомогательные горизонтальные линии
    for y in range(10, int(ylim[1]), 10):
        ax.axhline(y, color='gray', linestyle=':', linewidth=0.6, alpha=0.4)

    plt.tight_layout(rect=[0, 0, 0.8, 1])
    plt.show()

# --- Функция для создания графика воронки ---
def create_funnel_chart(df: pd.DataFrame):
    set_plot_style()

    # Агрегируем данные по всем городам
    total_data = df.groupby('day_order').agg({
        'cnt_order': 'sum',
        'cnt_offer': 'sum',
        'cnt_assign': 'sum',
        'cnt_arrive': 'sum',
        'cnt_trip': 'sum'
    }).sum()

    stages = ['Заявки', 'Предложения', 'Назначения', 'Прибытия', 'Поездки']
    values = [
        total_data['cnt_order'],
        total_data['cnt_offer'],
        total_data['cnt_assign'],
        total_data['cnt_arrive'],
        total_data['cnt_trip']
    ]

    # Расчет конверсий
    conversions = []
    conv_labels = []
    for i in range(1, len(values)):
        if values[i-1] > 0:
            conv = (values[i] / values[i-1]) * 100
            conversions.append(conv)
            conv_labels.append(f'{stages[i-1]} →\n{stages[i]}')
        else:
            conversions.append(0)
            conv_labels.append(f'{stages[i-1]} →\n{stages[i]}')

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))

    # График воронки с улучшенными цветами
    colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#16A085']
    bars = ax1.bar(stages, values, color=colors, alpha=0.85,
                   edgecolor='white', linewidth=2.5,
                   capstyle='round')

    # Улучшенное размещение подписей значений
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.02,
                f'{value:,}', ha='center', va='bottom',
                fontweight='bold', fontsize=13,
                bbox=dict(boxstyle='round,pad=0.4',
                         facecolor='white',
                         alpha=0.9,
                         edgecolor='gray',
                         linewidth=1))

    ax1.set_title('Воронка конверсии такси', fontsize=19, fontweight='bold', pad=30)
    ax1.set_ylabel('Количество', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.4, axis='y', linestyle='-', linewidth=0.8)
    ax1.set_ylim(0, max(values) * 1.25)
    ax1.set_axisbelow(True)
    ax1.tick_params(axis='x', labelsize=11, rotation=15)

    # График конверсий с улучшенными цветами
    conv_colors = ['#27AE60', '#E67E22', '#E74C3C', '#8E44AD']
    bars2 = ax2.bar(conv_labels, conversions, color=conv_colors, alpha=0.85,
                    edgecolor='white', linewidth=2.5,
                    capstyle='round')

    # Улучшенное размещение процентов
    for bar, conv in zip(bars2, conversions):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + max(conversions)*0.02,
                f'{conv:.1f}%', ha='center', va='bottom',
                fontweight='bold', fontsize=13,
                bbox=dict(boxstyle='round,pad=0.4',
                         facecolor='white',
                         alpha=0.9,
                         edgecolor='gray',
                         linewidth=1))

    ax2.set_title('Конверсии между этапами', fontsize=19, fontweight='bold', pad=30)
    ax2.set_ylabel('Конверсия (%)', fontsize=14, fontweight='bold')
    ax2.set_ylim(0, max(conversions) * 1.2)
    ax2.grid(True, alpha=0.4, axis='y', linestyle='-', linewidth=0.8)
    ax2.set_axisbelow(True)
    ax2.tick_params(axis='x', labelsize=10)

    plt.tight_layout(pad=4.0)
    plt.show()
    return fig

# --- Сравнение городов по типам заказов ---
def create_city_comparison_chart(df: pd.DataFrame):
    set_plot_style()
    city_colors, order_styles = get_color_schemes()

    if 'is_delayed' not in df.columns:
        print("Нет данных о типах заказов для сравнения")
        return

    # Агрегируем данные по городам и типам заказов
    city_data = df.groupby(['city', 'is_delayed']).agg({
        'cnt_order': 'sum',
        'cnt_trip': 'sum'
    }).reset_index()

    # Рассчитываем конверсию
    city_data['conversion'] = np.where(city_data['cnt_order'] > 0,
                                     city_data['cnt_trip'] / city_data['cnt_order'] * 100, 0)

    cities = city_data['city'].unique()
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))

    # График 1: Количество заказов по городам и типам
    x_pos = np.arange(len(cities))
    width = 0.35

    urgent_orders = []
    delayed_orders = []

    for city in cities:
        urgent = city_data[(city_data['city'] == city) & (city_data['is_delayed'] == False)]['cnt_order'].sum()
        delayed = city_data[(city_data['city'] == city) & (city_data['is_delayed'] == True)]['cnt_order'].sum()
        urgent_orders.append(urgent)
        delayed_orders.append(delayed)

    bars1 = ax1.bar(x_pos - width/2, urgent_orders, width,
                    label='Срочные заказы', color='#3498DB', alpha=0.8,
                    edgecolor='white', linewidth=2)
    bars2 = ax1.bar(x_pos + width/2, delayed_orders, width,
                    label='Отложенные заказы', color='#E74C3C', alpha=0.8,
                    edgecolor='white', linewidth=2)

    # Добавляем подписи на столбцы
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            if height > 0:
                ax1.text(bar.get_x() + bar.get_width()/2., height + max(max(urgent_orders), max(delayed_orders))*0.01,
                        f'{int(height):,}', ha='center', va='bottom',
                        fontweight='bold', fontsize=10,
                        bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))

    ax1.set_title('Количество заказов по городам и типам', fontsize=17, fontweight='bold', pad=25)
    ax1.set_xlabel('Города', fontsize=13, fontweight='bold')
    ax1.set_ylabel('Количество заказов', fontsize=13, fontweight='bold')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(cities, rotation=45, ha='right')
    ax1.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax1.grid(True, alpha=0.3, axis='y')
    ax1.set_axisbelow(True)

    # График 2: Конверсия по городам и типам
    urgent_conv = []
    delayed_conv = []

    for city in cities:
        urgent = city_data[(city_data['city'] == city) & (city_data['is_delayed'] == False)]['conversion'].values
        delayed = city_data[(city_data['city'] == city) & (city_data['is_delayed'] == True)]['conversion'].values
        urgent_conv.append(urgent[0] if len(urgent) > 0 else 0)
        delayed_conv.append(delayed[0] if len(delayed) > 0 else 0)

    bars3 = ax2.bar(x_pos - width/2, urgent_conv, width,
                    label='Срочные заказы', color='#2ECC71', alpha=0.8,
                    edgecolor='white', linewidth=2)
    bars4 = ax2.bar(x_pos + width/2, delayed_conv, width,
                    label='Отложенные заказы', color='#F39C12', alpha=0.8,
                    edgecolor='white', linewidth=2)

    # Добавляем подписи на столбцы
    for bars in [bars3, bars4]:
        for bar in bars:
            height = bar.get_height()
            if height > 0:
                ax2.text(bar.get_x() + bar.get_width()/2., height + max(max(urgent_conv), max(delayed_conv))*0.01,
                        f'{height:.1f}%', ha='center', va='bottom',
                        fontweight='bold', fontsize=10,
                        bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))

    ax2.set_title('Конверсия по городам и типам заказов', fontsize=17, fontweight='bold', pad=25)
    ax2.set_xlabel('Города', fontsize=13, fontweight='bold')
    ax2.set_ylabel('Конверсия (%)', fontsize=13, fontweight='bold')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(cities, rotation=45, ha='right')
    ax2.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    ax2.grid(True, alpha=0.3, axis='y')
    ax2.set_axisbelow(True)
    ax2.set_ylim(0, max(max(urgent_conv), max(delayed_conv)) * 1.15)

    plt.tight_layout(pad=4.0)
    plt.show()
    return fig

def plot_all_metrics(df: pd.DataFrame, cities: List[str] = None, segment_col: Optional[str] = None):
    # Сначала показываем воронку
    create_funnel_chart(df)

    # Затем показываем сравнение городов
    create_city_comparison_chart(df)

    # Затем показываем детальные метрики
    metrics = {
        'order2trip': 'Order2Trip - Базовая конверсия, %',
        'order2offer': 'Order2Offer - Конверсия из заказа в предложение водителю, %',
        'offer2assign': 'Offer2Assign - Конверсия из предложения в назначение водителя, %',
        'assign2arrive': 'Assign2Arrive - Конверсия из назначения в прибытие в т. А, %',
        'arrive2trip': 'Arrive2Trip - Конверсия из прибытия в т. А в т. Б, %'
    }
    for metric, title in metrics.items():
        plot_metric(df, metric, title, ylim=(0, 100), cities=cities, segment_col=segment_col)

# --- Основной сценарий ---
def main():
    df = load_and_prepare_data('taxi_data.xlsx')
    if df.empty:
        return
    df = mark_delayed_orders(df, threshold_min=10)
    agg = aggregate_metrics(df, segment_col='is_delayed')
    alert_outliers(agg, ['order2trip', 'order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip'])
    plot_all_metrics(agg, segment_col='is_delayed')

if __name__ == "__main__":
    main()
